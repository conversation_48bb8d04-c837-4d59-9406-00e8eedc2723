<!DOCTYPE html>
<html>
<head>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/webcamjs/1.0.26/webcam.min.js"></script>
</head>
<body>
  <div id="camera"></div>
  <button onclick="takeSnapshot()">Capture</button>
  <form id="uploadForm" method="POST" enctype="multipart/form-data" action="/process">
    <input type="file" name="id_image" required />
    <input type="hidden" name="face_image" id="face_image" />
    <button type="submit">Submit</button>
  </form>

  <script>
    Webcam.set({ width: 320, height: 240, image_format: 'jpeg', jpeg_quality: 90 });
    Webcam.attach('#camera');

    function takeSnapshot() {
      Webcam.snap(function(data_uri) {
        document.getElementById('face_image').value = data_uri;
      });
    }
  </script>
</body>
</html>
