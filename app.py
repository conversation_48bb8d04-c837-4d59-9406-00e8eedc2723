from flask import Flask, render_template, request
import requests
import base64
import pytesseract
from PIL import Image
import io

app = Flask(__name__)

OCR_API_URL = 'https://api.ocr.space/parse/image'
OCR_API_KEY = 'helloworld'  # free key

def call_ocr_space(image_bytes):
    response = requests.post(
        OCR_API_URL,
        files={'filename': image_bytes},
        data={'apikey': OCR_API_KEY}
    )
    return response.json()

def compare_faces(face1, face2):
    # Using Face++ API for comparing
    face_api_url = 'https://api-us.faceplusplus.com/facepp/v3/compare'
    api_key = '********************************'
    api_secret = 'EuDg0i5UPdzR8ZbmrPMb'
    
    response = requests.post(face_api_url, data={
        'api_key': api_key,
        'api_secret': api_secret,
    }, files={
        'image_base64_1': face1,
        'image_file2': face2
    })
    return response.json()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/process', methods=['POST'])
def process():
    face_data_uri = request.form['face_image']
    id_image_file = request.files['id_image']

    # Convert data URI to bytes
    header, encoded = face_data_uri.split(',', 1)
    face_bytes = base64.b64decode(encoded)

    # OCR on ID Image
    ocr_result = call_ocr_space(id_image_file)
    text_data = ocr_result['ParsedResults'][0]['ParsedText']

    # Simulated ID check
    if "1234" in text_data:
        id_verified = True
    else:
        id_verified = False

    # Face Match
    match_result = compare_faces(base64.b64encode(face_bytes), id_image_file)
    confidence = match_result.get('confidence', 0)

    return f"""
        <h3>ID Verified: {'Yes' if id_verified else 'No'}</h3>
        <h3>Face Match Confidence: {confidence}</h3>
        <pre>{text_data}</pre>
    """

if __name__ == '__main__':
    app.run(debug=True)
